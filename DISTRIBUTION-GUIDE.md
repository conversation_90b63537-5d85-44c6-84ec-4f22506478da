# 📦 OpenCode Custom - Distribution Guide

This guide explains how to package and distribute your customized OpenCode for other users.

## 🎯 Distribution Methods

### Method 1: GitHub Repository (Recommended)

1. **Push your code to GitHub:**
   ```bash
   git add .
   git commit -m "Custom OpenCode with npm deployment"
   git push origin main
   ```

2. **Users can then clone and deploy:**
   ```bash
   git clone https://github.com/yourusername/opencode-custom.git
   cd opencode-custom
   ./setup.sh
   ```

### Method 2: Distribution Package

1. **Create a distribution package:**
   ```bash
   chmod +x scripts/create-distribution.sh
   ./scripts/create-distribution.sh
   ```

2. **Upload the generated files:**
   - `opencode-custom-YYYYMMDD.tar.gz`
   - `opencode-custom-YYYYMMDD.zip`
   - Upload to your hosting platform

3. **Users download and extract:**
   ```bash
   tar -xzf opencode-custom-YYYYMMDD.tar.gz
   cd opencode-custom-YYYYMMDD
   ./setup.sh
   ```

### Method 3: One-Line Installer

1. **Host the install script:**
   - Upload `install.sh` to your web server
   - Make it accessible at `https://your-domain.com/install.sh`

2. **Users run one command:**
   ```bash
   curl -fsSL https://your-domain.com/install.sh | bash
   ```

### Method 4: Web-Based Installer

1. **Host the web installer:**
   - Upload `web-installer/index.html` to your web server
   - Update download URLs in the HTML

2. **Users visit your website:**
   - Go to `https://your-domain.com`
   - Follow the visual guide
   - Download and run setup

## 🔧 Customization for Distribution

### Update Repository URLs

1. **In `install.sh`:**
   ```bash
   REPO_URL="https://github.com/yourusername/opencode-custom"
   ```

2. **In `web-installer/index.html`:**
   ```javascript
   const baseUrl = 'https://your-domain.com/releases/';
   ```

### Customize Branding

1. **Update project name in scripts:**
   - Edit `setup.sh` and change `PROJECT_NAME`
   - Update `package.json` name field

2. **Customize documentation:**
   - Edit `USER-DEPLOYMENT-GUIDE.md`
   - Update `README-NPM-DEPLOYMENT.md`
   - Add your own branding/logos

### Pre-configure Settings

1. **Default configuration:**
   ```bash
   # Edit .env.example with sensible defaults
   PORT=8080
   HOSTNAME=0.0.0.0
   LOG_LEVEL=INFO
   ```

2. **Custom OpenCode config:**
   ```json
   // Edit opencode.json with your preferred models
   {
     "model": "anthropic/claude-3-5-sonnet-20241022",
     "small_model": "anthropic/claude-3-haiku-20240307"
   }
   ```

## 📋 User Instructions Template

### For GitHub Distribution

```markdown
# Quick Start

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/opencode-custom.git
   cd opencode-custom
   ```

2. Run setup:
   ```bash
   ./setup.sh
   ```

3. Follow the prompts to configure API keys and deploy!
```

### For Package Distribution

```markdown
# Quick Start

1. Download the latest release:
   - [Download .tar.gz](https://your-domain.com/releases/opencode-custom-latest.tar.gz)
   - [Download .zip](https://your-domain.com/releases/opencode-custom-latest.zip)

2. Extract and setup:
   ```bash
   tar -xzf opencode-custom-*.tar.gz
   cd opencode-custom-*
   ./setup.sh
   ```
```

### For One-Line Installation

```markdown
# One-Line Install

```bash
curl -fsSL https://your-domain.com/install.sh | bash
```

That's it! The script will handle everything automatically.
```

## 🚀 Hosting Options

### GitHub Releases

1. **Create a release:**
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```

2. **Upload distribution packages:**
   - Go to GitHub Releases
   - Upload the .tar.gz and .zip files
   - Users can download directly

### Static File Hosting

**Options:**
- **Netlify** - Free static hosting
- **Vercel** - Free static hosting  
- **GitHub Pages** - Free with GitHub
- **AWS S3** - Pay-per-use
- **Your own server** - Full control

**Setup:**
1. Upload distribution files
2. Update URLs in scripts
3. Test the download links

### Docker Registry

1. **Build and push Docker image:**
   ```bash
   docker build -f Dockerfile.npm -t yourusername/opencode-custom .
   docker push yourusername/opencode-custom
   ```

2. **Users can run directly:**
   ```bash
   docker run -p 8080:8080 \
     -e ANTHROPIC_API_KEY=your_key \
     yourusername/opencode-custom
   ```

## 📊 Analytics and Tracking

### Download Tracking

Add analytics to track downloads:

```html
<!-- In web-installer/index.html -->
<script>
function downloadFile(format) {
    // Track download
    gtag('event', 'download', {
        'event_category': 'opencode-custom',
        'event_label': format
    });
    
    // Proceed with download
    const link = document.createElement('a');
    link.href = `https://your-domain.com/releases/opencode-custom-latest.${format}`;
    link.download = `opencode-custom-latest.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
```

### Usage Analytics

Add telemetry to track usage (with user consent):

```javascript
// In your OpenCode deployment
if (process.env.ENABLE_TELEMETRY === 'true') {
    // Track deployment success, errors, etc.
}
```

## 🔒 Security Considerations

### Code Signing

For production distributions:

1. **Sign your releases:**
   ```bash
   gpg --armor --detach-sign opencode-custom-v1.0.0.tar.gz
   ```

2. **Provide checksums:**
   ```bash
   sha256sum opencode-custom-v1.0.0.tar.gz > checksums.txt
   ```

### Secure Distribution

1. **Use HTTPS** for all downloads
2. **Verify integrity** with checksums
3. **Sign releases** with GPG
4. **Scan for vulnerabilities** before distribution

## 📈 Version Management

### Semantic Versioning

Use semantic versioning for releases:
- `v1.0.0` - Major release
- `v1.1.0` - Minor features
- `v1.1.1` - Bug fixes

### Update Mechanism

Add update checking to your deployment:

```bash
# In setup.sh
check_for_updates() {
    local latest=$(curl -s https://api.github.com/repos/yourusername/opencode-custom/releases/latest | grep tag_name | cut -d'"' -f4)
    local current=$(cat VERSION 2>/dev/null || echo "v0.0.0")
    
    if [[ "$latest" != "$current" ]]; then
        echo "📢 Update available: $current → $latest"
        echo "   Visit: https://github.com/yourusername/opencode-custom/releases"
    fi
}
```

## 📚 Documentation Best Practices

### Essential Documentation

Include these files in your distribution:
- `README.md` - Quick start guide
- `USER-DEPLOYMENT-GUIDE.md` - Detailed instructions
- `TROUBLESHOOTING.md` - Common issues
- `CHANGELOG.md` - Version history
- `LICENSE` - License information

### User Support

1. **Create issue templates** in GitHub
2. **Provide troubleshooting guides**
3. **Set up discussions** for community support
4. **Document common problems** and solutions

## 🎉 Launch Checklist

Before distributing your custom OpenCode:

- [ ] Test installation on clean systems
- [ ] Verify all scripts work correctly
- [ ] Update all documentation
- [ ] Test with different API providers
- [ ] Check security configurations
- [ ] Prepare support channels
- [ ] Create release notes
- [ ] Test download links
- [ ] Verify one-line installer
- [ ] Test Docker deployment

## 📞 Support Strategy

### Community Support

1. **GitHub Discussions** - Q&A and community help
2. **Issue Tracker** - Bug reports and feature requests
3. **Documentation** - Comprehensive guides
4. **Examples** - Common use cases

### Professional Support

Consider offering:
- **Paid support** for enterprises
- **Custom deployments** for specific needs
- **Training** for teams
- **Consulting** for complex setups

---

**Ready to distribute?** Follow this guide to share your custom OpenCode with the world! 🚀

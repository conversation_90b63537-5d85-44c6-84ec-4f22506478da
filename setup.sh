#!/bin/bash

# OpenCode Custom - Automated Setup Script
# This script helps users deploy OpenCode with minimal effort

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="OpenCode Custom"

# Functions
print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🚀 OpenCode Custom Setup                  ║"
    echo "║                                                              ║"
    echo "║  This script will help you deploy your own OpenCode service ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

ask_user() {
    local question="$1"
    local default="$2"
    local response
    
    if [[ -n "$default" ]]; then
        read -p "$(echo -e "${YELLOW}?${NC} $question [${default}]: ")" response
        response=${response:-$default}
    else
        read -p "$(echo -e "${YELLOW}?${NC} $question: ")" response
    fi
    
    echo "$response"
}

check_command() {
    if command -v "$1" &> /dev/null; then
        log_success "$1 is installed"
        return 0
    else
        log_error "$1 is not installed"
        return 1
    fi
}

check_prerequisites() {
    log_step "Checking prerequisites..."
    
    local all_good=true
    
    # Check Node.js
    if check_command "node"; then
        local node_version=$(node --version | sed 's/v//')
        local major_version=$(echo $node_version | cut -d. -f1)
        if [[ $major_version -ge 18 ]]; then
            log_success "Node.js version $node_version (✓ >= 18)"
        else
            log_error "Node.js version $node_version is too old. Please install Node.js 18+"
            all_good=false
        fi
    else
        log_error "Please install Node.js 18+ from https://nodejs.org/"
        all_good=false
    fi
    
    # Check npm
    if check_command "npm"; then
        local npm_version=$(npm --version)
        log_success "npm version $npm_version"
    else
        log_error "npm is not available (should come with Node.js)"
        all_good=false
    fi
    
    # Check Docker (optional)
    if check_command "docker"; then
        log_success "Docker is available (recommended for production)"
        DOCKER_AVAILABLE=true
    else
        log_warning "Docker is not installed (optional, but recommended)"
        log_info "You can still use local deployment without Docker"
        DOCKER_AVAILABLE=false
    fi
    
    # Check Git
    if check_command "git"; then
        log_success "Git is available"
    else
        log_warning "Git is not installed (optional)"
    fi
    
    if [[ "$all_good" != true ]]; then
        log_error "Please install the missing prerequisites and run this script again"
        exit 1
    fi
    
    log_success "All required prerequisites are met!"
}

setup_environment() {
    log_step "Setting up environment configuration..."
    
    # Create .env file if it doesn't exist
    if [[ ! -f .env ]]; then
        log_info "Creating environment configuration..."
        cp .env.example .env
        log_success "Created .env file from template"
    else
        log_info ".env file already exists"
        local overwrite=$(ask_user "Do you want to reconfigure your environment?" "n")
        if [[ "$overwrite" =~ ^[Yy] ]]; then
            cp .env.example .env
            log_success "Reset .env file from template"
        fi
    fi
    
    # Ask for API keys
    echo ""
    log_info "🔑 Let's configure your AI provider API keys..."
    echo ""
    
    # Anthropic API Key
    echo -e "${CYAN}Anthropic Claude API Key:${NC}"
    echo "  • Get it from: https://console.anthropic.com/"
    echo "  • Format: sk-ant-api03-..."
    local anthropic_key=$(ask_user "Enter your Anthropic API key (or press Enter to skip)")
    
    if [[ -n "$anthropic_key" ]]; then
        sed -i.bak "s/ANTHROPIC_API_KEY=.*/ANTHROPIC_API_KEY=$anthropic_key/" .env
        log_success "Anthropic API key configured"
    fi
    
    # OpenAI API Key
    echo ""
    echo -e "${CYAN}OpenAI API Key:${NC}"
    echo "  • Get it from: https://platform.openai.com/api-keys"
    echo "  • Format: sk-..."
    local openai_key=$(ask_user "Enter your OpenAI API key (or press Enter to skip)")
    
    if [[ -n "$openai_key" ]]; then
        sed -i.bak "s/OPENAI_API_KEY=.*/OPENAI_API_KEY=$openai_key/" .env
        log_success "OpenAI API key configured"
    fi
    
    # Port configuration
    echo ""
    local port=$(ask_user "What port should the service run on?" "8080")
    sed -i.bak "s/PORT=.*/PORT=$port/" .env
    
    # Clean up backup files
    rm -f .env.bak
    
    log_success "Environment configuration complete!"
}

choose_deployment_method() {
    echo ""
    log_step "Choosing deployment method..."
    echo ""
    echo "Available deployment options:"
    echo ""
    echo -e "${GREEN}1. Local Development${NC} - Quick start, good for testing"
    echo "   • Runs directly on your machine"
    echo "   • Easy to modify and debug"
    echo "   • Single user"
    echo ""
    
    if [[ "$DOCKER_AVAILABLE" == true ]]; then
        echo -e "${GREEN}2. Docker Compose${NC} - Production ready, recommended"
        echo "   • Runs in containers"
        echo "   • Includes monitoring and security"
        echo "   • Multi-user capable"
        echo "   • Easy to scale"
        echo ""
    fi
    
    local choice
    if [[ "$DOCKER_AVAILABLE" == true ]]; then
        choice=$(ask_user "Choose deployment method (1 or 2)" "2")
    else
        choice=$(ask_user "Choose deployment method (1)" "1")
    fi
    
    case "$choice" in
        "1")
            DEPLOYMENT_METHOD="local"
            log_info "Selected: Local Development"
            ;;
        "2")
            if [[ "$DOCKER_AVAILABLE" == true ]]; then
                DEPLOYMENT_METHOD="compose"
                log_info "Selected: Docker Compose"
            else
                log_error "Docker is not available. Using local deployment."
                DEPLOYMENT_METHOD="local"
            fi
            ;;
        *)
            log_warning "Invalid choice. Using local deployment."
            DEPLOYMENT_METHOD="local"
            ;;
    esac
}

install_dependencies() {
    log_step "Installing dependencies..."
    
    log_info "Running npm install..."
    npm install
    
    log_success "Dependencies installed successfully!"
}

deploy_service() {
    log_step "Deploying OpenCode service..."
    
    # Make scripts executable
    chmod +x deploy.sh scripts/npm-deploy.js 2>/dev/null || true
    
    case "$DEPLOYMENT_METHOD" in
        "local")
            log_info "Starting local deployment..."
            ./deploy.sh local
            ;;
        "compose")
            log_info "Starting Docker Compose deployment..."
            ./deploy.sh compose
            ;;
    esac
    
    log_success "Deployment completed!"
}

show_success_message() {
    echo ""
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🎉 Setup Complete!                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    local port=$(grep "^PORT=" .env | cut -d= -f2)
    port=${port:-8080}
    
    echo -e "${CYAN}Your OpenCode service is now running!${NC}"
    echo ""
    echo "🌐 Access your service:"
    
    case "$DEPLOYMENT_METHOD" in
        "local")
            echo "   • API: http://localhost:$port"
            echo "   • Health check: http://localhost:$port/health"
            ;;
        "compose")
            echo "   • API: http://localhost:$port"
            echo "   • Web interface: http://localhost:80"
            echo "   • Health check: http://localhost:$port/health"
            if docker-compose -f docker-compose.npm.yml ps | grep -q grafana; then
                echo "   • Monitoring: http://localhost:3000 (admin/admin)"
            fi
            ;;
    esac
    
    echo ""
    echo "📋 Quick test:"
    echo "   curl http://localhost:$port/health"
    echo ""
    echo "🔧 Management commands:"
    case "$DEPLOYMENT_METHOD" in
        "local")
            echo "   • Stop: Ctrl+C in the terminal"
            echo "   • Restart: npm start"
            echo "   • Logs: Check the terminal output"
            ;;
        "compose")
            echo "   • Stop: docker-compose -f docker-compose.npm.yml down"
            echo "   • Restart: docker-compose -f docker-compose.npm.yml restart"
            echo "   • Logs: docker-compose -f docker-compose.npm.yml logs -f"
            ;;
    esac
    
    echo ""
    echo "📚 Documentation:"
    echo "   • User guide: USER-DEPLOYMENT-GUIDE.md"
    echo "   • npm deployment: README-NPM-DEPLOYMENT.md"
    echo ""
    echo -e "${GREEN}Happy coding with OpenCode! 🚀${NC}"
}

# Main setup flow
main() {
    print_header
    
    echo "This script will help you set up and deploy OpenCode Custom."
    echo "The process takes about 5-10 minutes depending on your internet speed."
    echo ""
    
    local proceed=$(ask_user "Ready to start?" "y")
    if [[ ! "$proceed" =~ ^[Yy] ]]; then
        log_info "Setup cancelled by user"
        exit 0
    fi
    
    check_prerequisites
    setup_environment
    choose_deployment_method
    install_dependencies
    deploy_service
    show_success_message
}

# Run main function
main "$@"

# OpenCode npm Deployment Guide

This guide explains how to deploy your customized OpenCode using npm instead of Bun.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm 8+
- Docker (optional, for containerized deployment)
- Git

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 3. Deploy

```bash
# Make deployment script executable
chmod +x deploy.sh

# Deploy locally
./deploy.sh local

# Or deploy with Docker
./deploy.sh docker

# Or deploy with Docker Compose (recommended for production)
./deploy.sh compose
```

## 📋 Deployment Options

### Local Development

```bash
# Development server
npm run dev

# Production build and run
npm run build
npm start
```

### Docker Deployment

```bash
# Build Docker image
docker build -f Dockerfile.npm -t opencode-custom .

# Run container
docker run -p 8080:8080 \
  -e ANTHROPIC_API_KEY=your_key \
  -e OPENAI_API_KEY=your_key \
  opencode-custom
```

### Docker Compose (Recommended)

```bash
# Start all services
docker-compose -f docker-compose.npm.yml up -d

# View logs
docker-compose -f docker-compose.npm.yml logs -f

# Stop services
docker-compose -f docker-compose.npm.yml down
```

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```bash
# Server
PORT=8080
HOSTNAME=0.0.0.0
NODE_ENV=production

# AI Providers
ANTHROPIC_API_KEY=your_anthropic_key
OPENAI_API_KEY=your_openai_key
ZHIPU_API_KEY=your_zhipu_key

# Paths
OPENCODE_DATA_PATH=/app/data
OPENCODE_CONFIG_PATH=/app/data/config
```

### OpenCode Configuration

Create `opencode.json` in your project root:

```json
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "anthropic": {
      "type": "api",
      "key": "${ANTHROPIC_API_KEY}"
    },
    "openai": {
      "type": "api", 
      "key": "${OPENAI_API_KEY}"
    }
  },
  "model": "anthropic/claude-3-5-sonnet-20241022",
  "small_model": "anthropic/claude-3-haiku-20240307"
}
```

## 🏗️ Build Process

The npm build process includes:

1. **TypeScript Compilation**: Converts TS to JS
2. **Asset Copying**: Copies tool templates and configs
3. **Dependency Resolution**: Installs production dependencies
4. **Optimization**: Minifies and optimizes for production

### Manual Build

```bash
# Type check
npm run typecheck

# Build TypeScript
npm run build

# Or use the deployment script
node scripts/npm-deploy.js local
```

## 🐳 Docker Services

The Docker Compose setup includes:

- **OpenCode**: Main application server
- **Nginx**: Reverse proxy with SSL termination
- **Redis**: Caching layer (optional)
- **Prometheus**: Metrics collection (optional)
- **Grafana**: Monitoring dashboard (optional)

### Service URLs

- OpenCode API: `http://localhost:8080`
- Nginx Proxy: `http://localhost:80` / `https://localhost:443`
- Grafana: `http://localhost:3000` (admin/admin)
- Prometheus: `http://localhost:9090`

## 🔒 Security

### SSL/TLS Setup

1. Generate SSL certificates:

```bash
# Self-signed for development
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem
```

2. For production, use Let's Encrypt or your certificate provider

### Security Headers

The nginx configuration includes:
- HSTS
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Rate limiting

## 📊 Monitoring

### Health Checks

- Application: `GET /health`
- Docker: Built-in health checks
- Nginx: Load balancer health checks

### Logs

```bash
# Application logs
docker-compose -f docker-compose.npm.yml logs opencode

# Nginx logs
docker-compose -f docker-compose.npm.yml logs nginx

# All logs
docker-compose -f docker-compose.npm.yml logs -f
```

### Metrics

Enable monitoring services:

```bash
# Start with monitoring
docker-compose -f docker-compose.npm.yml --profile monitoring up -d
```

## 🚀 Production Deployment

### 1. Server Setup

```bash
# Clone your repository
git clone https://github.com/yourusername/opencode-custom.git
cd opencode-custom

# Set up environment
cp .env.example .env
# Edit .env with production values
```

### 2. Deploy

```bash
# Full production deployment
./deploy.sh production
```

### 3. Domain Setup

Update your DNS to point to your server, then update nginx configuration:

```nginx
server_name your-domain.com;
```

### 4. SSL Certificate

```bash
# Using certbot for Let's Encrypt
certbot --nginx -d your-domain.com
```

## 🔧 Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in `.env`
2. **Permission errors**: Check file permissions
3. **Memory issues**: Increase Docker memory limits
4. **SSL errors**: Verify certificate paths

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
export ENABLE_DEBUG_MODE=true

# Run with debug
npm run dev
```

### Container Debugging

```bash
# Enter container
docker exec -it opencode-custom bash

# Check logs
docker logs opencode-custom

# Check processes
docker exec opencode-custom ps aux
```

## 📝 Customization

### Adding Custom Providers

1. Update `opencode.json`:

```json
{
  "provider": {
    "custom": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "Custom Provider",
      "options": {
        "baseURL": "https://your-api.com/v1"
      }
    }
  }
}
```

2. Rebuild and deploy:

```bash
npm run build
./deploy.sh docker
```

### Custom Tools

Add custom tools in `packages/opencode/src/tool/` and rebuild.

## 📚 Additional Resources

- [OpenCode Documentation](https://opencode.ai/docs)
- [Docker Documentation](https://docs.docker.com/)
- [Nginx Documentation](https://nginx.org/en/docs/)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)

## 🆘 Support

If you encounter issues:

1. Check the logs
2. Verify your configuration
3. Ensure all dependencies are installed
4. Check the GitHub issues for similar problems

## 📄 License

This deployment configuration is provided under the same license as OpenCode.

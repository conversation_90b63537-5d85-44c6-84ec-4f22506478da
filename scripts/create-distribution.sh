#!/bin/bash

# Create Distribution Package Script
# This script creates a clean distribution package for users

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
DIST_NAME="opencode-custom-$(date +%Y%m%d)"
DIST_DIR="$PROJECT_ROOT/dist-package"
PACKAGE_DIR="$DIST_DIR/$DIST_NAME"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

create_distribution() {
    log_info "Creating distribution package..."
    
    # Clean and create directories
    rm -rf "$DIST_DIR"
    mkdir -p "$PACKAGE_DIR"
    
    # Copy essential files
    log_info "Copying source files..."
    
    # Core application files
    cp -r "$PROJECT_ROOT/packages" "$PACKAGE_DIR/"
    cp -r "$PROJECT_ROOT/cloud" "$PACKAGE_DIR/"
    cp -r "$PROJECT_ROOT/infra" "$PACKAGE_DIR/"
    cp -r "$PROJECT_ROOT/scripts" "$PACKAGE_DIR/"
    cp -r "$PROJECT_ROOT/nginx" "$PACKAGE_DIR/"
    
    # Configuration files
    cp "$PROJECT_ROOT/package.json" "$PACKAGE_DIR/"
    cp "$PROJECT_ROOT/tsconfig.json" "$PACKAGE_DIR/"
    cp "$PROJECT_ROOT/opencode.json" "$PACKAGE_DIR/"
    cp "$PROJECT_ROOT/.env.example" "$PACKAGE_DIR/"
    
    # Docker files
    cp "$PROJECT_ROOT/Dockerfile.npm" "$PACKAGE_DIR/"
    cp "$PROJECT_ROOT/docker-compose.npm.yml" "$PACKAGE_DIR/"
    
    # Deployment scripts
    cp "$PROJECT_ROOT/deploy.sh" "$PACKAGE_DIR/"
    cp "$PROJECT_ROOT/setup.sh" "$PACKAGE_DIR/"
    
    # Documentation
    cp "$PROJECT_ROOT/README-NPM-DEPLOYMENT.md" "$PACKAGE_DIR/"
    cp "$PROJECT_ROOT/USER-DEPLOYMENT-GUIDE.md" "$PACKAGE_DIR/"
    cp "$PROJECT_ROOT/LICENSE" "$PACKAGE_DIR/" 2>/dev/null || true
    
    # Make scripts executable
    chmod +x "$PACKAGE_DIR/setup.sh"
    chmod +x "$PACKAGE_DIR/deploy.sh"
    chmod +x "$PACKAGE_DIR/scripts"/*.sh 2>/dev/null || true
    chmod +x "$PACKAGE_DIR/scripts"/*.js 2>/dev/null || true
    
    log_success "Files copied successfully"
}

create_readme() {
    log_info "Creating distribution README..."
    
    cat > "$PACKAGE_DIR/README.md" << 'EOF'
# 🚀 OpenCode Custom - Ready to Deploy

This is a customized version of OpenCode that runs with npm instead of Bun, with enhanced deployment options.

## ⚡ Quick Start

1. **Run the setup script:**
   ```bash
   ./setup.sh
   ```

2. **That's it!** The script will guide you through:
   - Installing dependencies
   - Configuring API keys
   - Choosing deployment method
   - Starting the service

## 📋 What You Need

- Node.js 18+ ([Download](https://nodejs.org/))
- npm 8+ (comes with Node.js)
- Docker (optional, for production deployment)

## 🎯 Deployment Options

### Option 1: Automated Setup (Recommended)
```bash
./setup.sh
```

### Option 2: Manual Setup
```bash
# Install dependencies
npm install

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Deploy locally
./deploy.sh local

# Or deploy with Docker
./deploy.sh compose
```

## 🔑 API Keys Required

You'll need at least one of these:
- **Anthropic API Key** - Get from [console.anthropic.com](https://console.anthropic.com/)
- **OpenAI API Key** - Get from [platform.openai.com](https://platform.openai.com/api-keys)

## 📚 Documentation

- **[User Deployment Guide](USER-DEPLOYMENT-GUIDE.md)** - Complete user guide
- **[npm Deployment Details](README-NPM-DEPLOYMENT.md)** - Technical details

## 🆘 Need Help?

1. Check the [User Deployment Guide](USER-DEPLOYMENT-GUIDE.md)
2. Look at the troubleshooting section
3. Open an issue in the repository

## 🌟 Features

- ✅ npm compatibility (no Bun required)
- ✅ Docker deployment ready
- ✅ Production security hardening
- ✅ Monitoring and logging
- ✅ Easy setup scripts
- ✅ Comprehensive documentation

---

**Ready to start?** Run `./setup.sh` and follow the prompts!
EOF

    log_success "README created"
}

create_install_script() {
    log_info "Creating one-line installer..."
    
    cat > "$PACKAGE_DIR/install.sh" << 'EOF'
#!/bin/bash

# OpenCode Custom - One-line installer
# Usage: curl -fsSL https://your-domain.com/install.sh | bash

set -e

REPO_URL="https://github.com/yourusername/opencode-custom"
INSTALL_DIR="$HOME/opencode-custom"

echo "🚀 Installing OpenCode Custom..."

# Check prerequisites
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm is required. Please install npm."
    exit 1
fi

# Clone or download
if command -v git &> /dev/null; then
    echo "📥 Cloning repository..."
    git clone "$REPO_URL" "$INSTALL_DIR"
else
    echo "📥 Downloading repository..."
    curl -L "$REPO_URL/archive/main.zip" -o /tmp/opencode-custom.zip
    unzip /tmp/opencode-custom.zip -d /tmp/
    mv /tmp/opencode-custom-main "$INSTALL_DIR"
    rm /tmp/opencode-custom.zip
fi

# Run setup
cd "$INSTALL_DIR"
chmod +x setup.sh
echo "🔧 Starting setup..."
./setup.sh

echo "✅ Installation complete!"
EOF

    chmod +x "$PACKAGE_DIR/install.sh"
    log_success "Install script created"
}

create_package_info() {
    log_info "Creating package information..."
    
    cat > "$PACKAGE_DIR/PACKAGE-INFO.md" << EOF
# Package Information

**Package:** $DIST_NAME
**Created:** $(date)
**Version:** Custom npm deployment

## Contents

- \`packages/\` - Core OpenCode application
- \`cloud/\` - Cloud deployment configurations  
- \`infra/\` - Infrastructure as code
- \`scripts/\` - Build and deployment scripts
- \`nginx/\` - Reverse proxy configuration
- \`setup.sh\` - Automated setup script
- \`deploy.sh\` - Deployment script
- \`*.md\` - Documentation files

## Quick Start

1. Run \`./setup.sh\`
2. Follow the prompts
3. Access your service at http://localhost:8080

## Support

- Read USER-DEPLOYMENT-GUIDE.md for detailed instructions
- Check README-NPM-DEPLOYMENT.md for technical details
- Open issues in the repository for bugs

EOF

    log_success "Package info created"
}

create_archive() {
    log_info "Creating archive..."
    
    cd "$DIST_DIR"
    
    # Create tar.gz
    tar -czf "$DIST_NAME.tar.gz" "$DIST_NAME"
    log_success "Created $DIST_NAME.tar.gz"
    
    # Create zip
    zip -r "$DIST_NAME.zip" "$DIST_NAME" > /dev/null
    log_success "Created $DIST_NAME.zip"
    
    # Show sizes
    echo ""
    echo "📦 Package sizes:"
    ls -lh "$DIST_NAME".* | awk '{print "   " $9 ": " $5}'
}

show_distribution_info() {
    echo ""
    echo -e "${GREEN}🎉 Distribution package created successfully!${NC}"
    echo ""
    echo "📁 Location: $DIST_DIR"
    echo ""
    echo "📦 Files created:"
    echo "   • $DIST_NAME/ (directory)"
    echo "   • $DIST_NAME.tar.gz (archive)"
    echo "   • $DIST_NAME.zip (archive)"
    echo ""
    echo "🚀 To distribute:"
    echo "   1. Upload the archive to your preferred hosting"
    echo "   2. Share the download link with users"
    echo "   3. Users can extract and run ./setup.sh"
    echo ""
    echo "💡 One-line install (after hosting):"
    echo "   curl -fsSL https://your-domain.com/install.sh | bash"
    echo ""
    echo "📋 Next steps:"
    echo "   • Test the package by extracting and running setup.sh"
    echo "   • Update the repository URLs in install.sh"
    echo "   • Host the files on your preferred platform"
}

# Main function
main() {
    echo "🏗️  Creating OpenCode Custom Distribution Package"
    echo "================================================="
    echo ""
    
    create_distribution
    create_readme
    create_install_script
    create_package_info
    create_archive
    show_distribution_info
}

# Run main function
main "$@"
EOF

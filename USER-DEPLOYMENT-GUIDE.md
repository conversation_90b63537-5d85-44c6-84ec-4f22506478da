# 🚀 Deploy OpenCode Custom - User Guide

This guide helps you deploy a customized version of OpenCode using npm and Docker.

## 📋 Prerequisites

Before you start, make sure you have:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **npm 8+** (comes with Node.js)
- **Docker** (optional but recommended) - [Download here](https://www.docker.com/get-started)
- **Git** - [Download here](https://git-scm.com/)

## 🎯 Quick Start (5 minutes)

### Step 1: Get the Code

```bash
# Clone the repository
git clone https://github.com/yourusername/opencode-custom.git
cd opencode-custom

# Or download and extract the ZIP file
```

### Step 2: One-Command Setup

```bash
# Run the setup script (this will guide you through everything)
./setup.sh
```

That's it! The setup script will:
- Check your system requirements
- Install dependencies
- Help you configure API keys
- Deploy the service
- Show you how to access it

## 🔧 Manual Setup (if you prefer step-by-step)

### Step 1: Install Dependencies

```bash
npm install
```

### Step 2: Configure Your API Keys

```bash
# Copy the example configuration
cp .env.example .env

# Edit the configuration file
nano .env  # or use your preferred editor
```

**Required API Keys:**
- `ANTHROPIC_API_KEY` - Get from [Anthropic Console](https://console.anthropic.com/)
- `OPENAI_API_KEY` - Get from [OpenAI Platform](https://platform.openai.com/)

**Example .env file:**
```bash
# AI Provider API Keys
ANTHROPIC_API_KEY=sk-ant-api03-your-key-here
OPENAI_API_KEY=sk-your-openai-key-here

# Server Configuration (you can leave these as default)
PORT=8080
HOSTNAME=0.0.0.0
NODE_ENV=production
```

### Step 3: Choose Your Deployment Method

#### Option A: Simple Local Deployment
```bash
./deploy.sh local
```
✅ **Best for**: Testing, development, single-user setups

#### Option B: Docker Deployment (Recommended)
```bash
./deploy.sh compose
```
✅ **Best for**: Production, multi-user, better performance and security

## 🌐 Accessing Your OpenCode Service

After deployment, you can access OpenCode at:

- **Local deployment**: `http://localhost:8080`
- **Docker deployment**: `http://localhost:80`

### Using the API

```bash
# Health check
curl http://localhost:8080/health

# Example API call
curl -X POST http://localhost:8080/api/session \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, OpenCode!"}'
```

### Using with OpenCode CLI

If you have the OpenCode CLI installed:

```bash
# Point to your custom deployment
export OPENCODE_API_URL=http://localhost:8080

# Use OpenCode normally
opencode run "Write a hello world function"
```

## 🔧 Configuration Options

### Basic Configuration

Edit `opencode.json` in the project root:

```json
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "anthropic": {
      "type": "api",
      "key": "${ANTHROPIC_API_KEY}"
    }
  },
  "model": "anthropic/claude-3-5-sonnet-20241022",
  "small_model": "anthropic/claude-3-haiku-20240307"
}
```

### Advanced Configuration

For advanced users, you can modify:

- **Server settings**: Edit `.env`
- **AI models**: Edit `opencode.json`
- **Docker services**: Edit `docker-compose.npm.yml`
- **Nginx proxy**: Edit `nginx/nginx.conf`

## 🛠️ Common Use Cases

### For Personal Use
```bash
# Simple setup for personal coding assistant
./deploy.sh local
```

### For Team/Organization
```bash
# Production setup with monitoring and security
./deploy.sh compose

# Optional: Enable monitoring dashboard
docker-compose -f docker-compose.npm.yml --profile monitoring up -d
```

### For Development/Testing
```bash
# Development mode with hot reload
npm run dev
```

## 🔍 Monitoring and Logs

### Check Service Status
```bash
# For Docker deployment
docker-compose -f docker-compose.npm.yml ps

# Check logs
docker-compose -f docker-compose.npm.yml logs -f opencode
```

### Access Monitoring Dashboard
If you enabled monitoring:
- **Grafana**: `http://localhost:3000` (admin/admin)
- **Prometheus**: `http://localhost:9090`

## 🆘 Troubleshooting

### Common Issues

**1. Port already in use**
```bash
# Change port in .env file
PORT=8081
```

**2. API key not working**
```bash
# Verify your API key in .env
# Make sure there are no extra spaces or quotes
```

**3. Docker permission errors**
```bash
# Add your user to docker group (Linux)
sudo usermod -aG docker $USER
# Then logout and login again
```

**4. Service not starting**
```bash
# Check logs
docker-compose -f docker-compose.npm.yml logs opencode

# Restart services
docker-compose -f docker-compose.npm.yml restart
```

### Getting Help

1. **Check the logs** first:
   ```bash
   docker-compose -f docker-compose.npm.yml logs -f
   ```

2. **Verify your configuration**:
   ```bash
   # Test API keys
   curl -H "Authorization: Bearer $ANTHROPIC_API_KEY" \
     https://api.anthropic.com/v1/messages
   ```

3. **Reset everything**:
   ```bash
   # Stop all services
   docker-compose -f docker-compose.npm.yml down
   
   # Remove containers and volumes
   docker-compose -f docker-compose.npm.yml down -v
   
   # Start fresh
   ./deploy.sh compose
   ```

## 🔄 Updates and Maintenance

### Updating the Service
```bash
# Pull latest changes
git pull origin main

# Rebuild and redeploy
./deploy.sh compose
```

### Backup Your Data
```bash
# Backup configuration and data
docker-compose -f docker-compose.npm.yml exec opencode \
  tar -czf /app/backup-$(date +%Y%m%d).tar.gz /app/data
```

### Stop the Service
```bash
# Stop all services
docker-compose -f docker-compose.npm.yml down

# Stop and remove everything (including data)
docker-compose -f docker-compose.npm.yml down -v
```

## 🌟 What's Different in This Custom Version?

This customized OpenCode includes:
- ✅ npm compatibility (works without Bun)
- ✅ Enhanced Docker deployment
- ✅ Production-ready configuration
- ✅ Monitoring and logging
- ✅ Security hardening
- ✅ Easy setup scripts

## 📚 Next Steps

1. **Explore the API**: Check out the OpenAPI docs at `http://localhost:8080/docs`
2. **Customize further**: Modify `opencode.json` for your specific needs
3. **Integrate**: Use the API in your applications or workflows
4. **Scale**: Deploy to cloud providers using the Docker images

## 🤝 Contributing

Found an issue or want to improve this deployment?
1. Fork the repository
2. Make your changes
3. Submit a pull request

## 📄 License

This deployment configuration follows the same license as OpenCode.

---

**Need help?** Open an issue in the repository or check the troubleshooting section above.

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenCode Custom - Easy Deployment</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #666;
        }
        
        .step {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            transition: border-color 0.3s;
        }
        
        .step:hover {
            border-color: #667eea;
        }
        
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .step h3 {
            display: inline-block;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            position: relative;
            overflow-x: auto;
        }
        
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .copy-btn:hover {
            background: #5a6fd8;
        }
        
        .download-section {
            text-align: center;
            margin: 2rem 0;
        }
        
        .download-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1.1rem;
            margin: 0.5rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature {
            padding: 1rem;
            border-radius: 8px;
            background: #f8f9fa;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .requirements {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .requirements h4 {
            color: #856404;
            margin-bottom: 0.5rem;
        }
        
        .requirements ul {
            margin-left: 1.5rem;
            color: #856404;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .step {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <h1>🚀 OpenCode Custom</h1>
                <p>Deploy your own AI coding assistant in minutes</p>
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h4>Easy Setup</h4>
                    <p>One command installation</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🐳</div>
                    <h4>Docker Ready</h4>
                    <p>Production deployment</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔒</div>
                    <h4>Secure</h4>
                    <p>Built-in security features</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h4>Monitoring</h4>
                    <p>Grafana dashboards included</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>📋 Prerequisites</h2>
            <div class="requirements">
                <h4>Required:</h4>
                <ul>
                    <li>Node.js 18+ (<a href="https://nodejs.org/" target="_blank">Download</a>)</li>
                    <li>npm 8+ (comes with Node.js)</li>
                    <li>At least one AI provider API key</li>
                </ul>
            </div>
            <div class="requirements">
                <h4>Optional (but recommended):</h4>
                <ul>
                    <li>Docker (<a href="https://www.docker.com/get-started" target="_blank">Download</a>)</li>
                    <li>Git (<a href="https://git-scm.com/" target="_blank">Download</a>)</li>
                </ul>
            </div>
        </div>
        
        <div class="card">
            <h2>🎯 Quick Start</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <h3>Download OpenCode Custom</h3>
                <div class="download-section">
                    <a href="#" class="download-btn" onclick="downloadFile('tar.gz')">
                        📦 Download .tar.gz
                    </a>
                    <a href="#" class="download-btn" onclick="downloadFile('zip')">
                        📦 Download .zip
                    </a>
                </div>
                <div class="alert alert-info">
                    <strong>Or use one-line installer:</strong>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyToClipboard('curl -fsSL https://your-domain.com/install.sh | bash')">Copy</button>
                        curl -fsSL https://your-domain.com/install.sh | bash
                    </div>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <h3>Extract and Enter Directory</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard('tar -xzf opencode-custom-*.tar.gz\ncd opencode-custom-*')">Copy</button>
                    # For .tar.gz
tar -xzf opencode-custom-*.tar.gz
cd opencode-custom-*

# For .zip
unzip opencode-custom-*.zip
cd opencode-custom-*
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <h3>Run Setup Script</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard('./setup.sh')">Copy</button>
                    ./setup.sh
                </div>
                <p>The setup script will:</p>
                <ul style="margin-left: 2rem; margin-top: 0.5rem;">
                    <li>Check your system requirements</li>
                    <li>Install dependencies</li>
                    <li>Help you configure API keys</li>
                    <li>Choose deployment method</li>
                    <li>Start the service</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <h3>Access Your Service</h3>
                <p>After setup completes, access your OpenCode service at:</p>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard('http://localhost:8080')">Copy</button>
                    http://localhost:8080
                </div>
                <p>Test with a health check:</p>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard('curl http://localhost:8080/health')">Copy</button>
                    curl http://localhost:8080/health
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🔑 API Keys</h2>
            <p>You'll need at least one AI provider API key:</p>
            
            <div style="margin: 1rem 0;">
                <h4>🤖 Anthropic Claude</h4>
                <p>Get your API key from <a href="https://console.anthropic.com/" target="_blank">console.anthropic.com</a></p>
                <p><strong>Format:</strong> <code>sk-ant-api03-...</code></p>
            </div>
            
            <div style="margin: 1rem 0;">
                <h4>🧠 OpenAI</h4>
                <p>Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">platform.openai.com</a></p>
                <p><strong>Format:</strong> <code>sk-...</code></p>
            </div>
        </div>
        
        <div class="card">
            <h2>🛠️ Manual Setup (Alternative)</h2>
            <p>If you prefer manual setup:</p>
            
            <div class="code-block">
                <button class="copy-btn" onclick="copyToClipboard('npm install\ncp .env.example .env\n# Edit .env with your API keys\n./deploy.sh local')">Copy</button>
# Install dependencies
npm install

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Deploy locally
./deploy.sh local

# Or deploy with Docker
./deploy.sh compose
            </div>
        </div>
        
        <div class="card">
            <h2>📚 Documentation</h2>
            <ul style="margin-left: 1.5rem;">
                <li><strong>USER-DEPLOYMENT-GUIDE.md</strong> - Complete user guide</li>
                <li><strong>README-NPM-DEPLOYMENT.md</strong> - Technical deployment details</li>
                <li><strong>Troubleshooting</strong> - Common issues and solutions</li>
            </ul>
        </div>
        
        <div class="card">
            <h2>🆘 Need Help?</h2>
            <p>If you run into issues:</p>
            <ol style="margin-left: 1.5rem;">
                <li>Check the documentation files included in the package</li>
                <li>Look at the troubleshooting section</li>
                <li>Open an issue in the repository</li>
            </ol>
        </div>
    </div>
    
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show feedback
                event.target.textContent = 'Copied!';
                setTimeout(() => {
                    event.target.textContent = 'Copy';
                }, 2000);
            });
        }
        
        function downloadFile(format) {
            // Replace with your actual download URLs
            const baseUrl = 'https://your-domain.com/releases/';
            const filename = `opencode-custom-latest.${format}`;
            
            // Create download link
            const link = document.createElement('a');
            link.href = baseUrl + filename;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>

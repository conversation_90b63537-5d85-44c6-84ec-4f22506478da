# Use Node.js 22 (required by the project)
FROM node:22-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    python3 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY packages/opencode/package.json ./packages/opencode/
COPY packages/*/package.json ./packages/*/

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Install tsx globally for TypeScript execution
RUN npm install -g tsx

# Create a non-root user
RUN useradd -m -u 1001 opencode
RUN chown -R opencode:opencode /app
USER opencode

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Start the server
CMD ["node", "npm-serve.js", "--port", "8080", "--hostname", "0.0.0.0"]

# Multi-stage build for production
FROM node:22-slim AS builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    python3 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY packages/opencode/package.json ./packages/opencode/
COPY packages/*/package.json ./packages/*/

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:22-slim AS production

# Install runtime system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    ripgrep \
    fzf \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create a non-root user
RUN useradd -m -u 1001 opencode

# Copy built application from builder stage
COPY --from=builder /app/dist ./
COPY --from=builder /app/node_modules ./node_modules

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Set ownership
RUN chown -R opencode:opencode /app

# Switch to non-root user
USER opencode

# Create data and logs directories
RUN mkdir -p /app/data /app/logs

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Environment variables
ENV NODE_ENV=production
ENV PORT=8080
ENV HOSTNAME=0.0.0.0

# Start the server
CMD ["node", "index.js"]

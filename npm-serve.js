#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Parse command line arguments
const args = process.argv.slice(2);
const port = args.includes('--port') ? args[args.indexOf('--port') + 1] : '8080';
const hostname = args.includes('--hostname') ? args[args.indexOf('--hostname') + 1] : '127.0.0.1';

console.log(`Starting opencode server on ${hostname}:${port}`);

// Start the TypeScript server using tsx
const serverProcess = spawn('npx', [
  'tsx',
  join(__dirname, 'packages/opencode/src/index.ts'),
  'serve',
  '--port', port,
  '--hostname', hostname
], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production'
  }
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  serverProcess.kill('SIGTERM');
  process.exit(0);
});

serverProcess.on('exit', (code) => {
  console.log(`Server process exited with code ${code}`);
  process.exit(code);
});
